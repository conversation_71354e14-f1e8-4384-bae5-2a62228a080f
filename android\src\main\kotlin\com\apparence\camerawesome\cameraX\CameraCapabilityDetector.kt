package com.apparence.camerawesome.cameraX

import android.annotation.SuppressLint
import android.content.Context
import android.hardware.camera2.CameraCharacteristics
import android.hardware.camera2.CameraManager
import android.hardware.camera2.CameraMetadata
import android.os.Build
import android.util.Log
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.Camera
import com.apparence.camerawesome.CamerawesomePlugin

/**
 * Detects camera hardware capabilities and determines the best implementation strategy
 * for manual shutter speed control based on device capabilities.
 */
class CameraCapabilityDetector(private val context: Context) {
    
    companion object {
        private const val TAG = "CameraCapabilityDetector"
    }
    
    enum class CameraLevel {
        LEGACY,      // Basic camera support, limited manual controls
        LIMITED,     // Some manual controls available
        FULL,        // Full Camera2 API support
        LEVEL_3      // Advanced features, RAW support, etc.
    }
    
    enum class ShutterSpeedStrategy {
        DIRECT_CAMERA2,           // Use Camera2 API directly for best results
        CAMERAX_INTEROP_ADVANCED, // Use CameraX with advanced Camera2 interop
        CAMERAX_INTEROP_BASIC,    // Use CameraX with basic Camera2 interop
        EXPOSURE_COMPENSATION     // Fallback to exposure compensation
    }
    
    data class CameraCapabilities(
        val level: CameraLevel,
        val strategy: ShutterSpeedStrategy,
        val supportsManualExposure: Boolean,
        val supportsRaw: Boolean,
        val supportsAdvancedFeatures: Boolean,
        val minExposureTimeNs: Long,
        val maxExposureTimeNs: Long,
        val supportedIsoRange: IntRange?,
        val deviceInfo: String
    )
    
    @SuppressLint("RestrictedApi")
    fun detectCapabilities(cameraId: String = "0"): CameraCapabilities {
        return try {
            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            val characteristics = cameraManager.getCameraCharacteristics(cameraId)
            
            val hardwareLevel = getHardwareLevel(characteristics)
            val cameraLevel = mapHardwareLevel(hardwareLevel)
            
            val manualExposureSupported = isManualExposureSupported(characteristics)
            val rawSupported = isRawSupported(characteristics)
            val advancedFeaturesSupported = areAdvancedFeaturesSupported(characteristics)
            
            val exposureTimeRange = getExposureTimeRange(characteristics)
            val isoRange = getIsoRange(characteristics)
            
            val strategy = determineStrategy(cameraLevel, manualExposureSupported, advancedFeaturesSupported)
            
            val deviceInfo = buildDeviceInfo(hardwareLevel, manualExposureSupported, rawSupported)
            
            val capabilities = CameraCapabilities(
                level = cameraLevel,
                strategy = strategy,
                supportsManualExposure = manualExposureSupported,
                supportsRaw = rawSupported,
                supportsAdvancedFeatures = advancedFeaturesSupported,
                minExposureTimeNs = exposureTimeRange.first,
                maxExposureTimeNs = exposureTimeRange.second,
                supportedIsoRange = isoRange,
                deviceInfo = deviceInfo
            )
            
            Log.i(TAG, "Camera capabilities detected: $capabilities")
            capabilities
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to detect camera capabilities", e)
            // Return safe fallback capabilities
            CameraCapabilities(
                level = CameraLevel.LEGACY,
                strategy = ShutterSpeedStrategy.EXPOSURE_COMPENSATION,
                supportsManualExposure = false,
                supportsRaw = false,
                supportsAdvancedFeatures = false,
                minExposureTimeNs = 1000000L, // 1ms
                maxExposureTimeNs = 1000000000L, // 1s
                supportedIsoRange = null,
                deviceInfo = "Unknown device (fallback)"
            )
        }
    }
    
    @OptIn(ExperimentalCamera2Interop::class)
    fun detectCapabilitiesFromCamera(camera: Camera): CameraCapabilities {
        return try {
            val camera2Info = Camera2CameraInfo.from(camera.cameraInfo)
            val cameraId = camera2Info.cameraId
            detectCapabilities(cameraId)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to detect capabilities from Camera object", e)
            detectCapabilities() // Fallback to default camera
        }
    }
    
    private fun getHardwareLevel(characteristics: CameraCharacteristics): Int {
        return characteristics.get(CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL)
            ?: CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_LEGACY
    }
    
    private fun mapHardwareLevel(hardwareLevel: Int): CameraLevel {
        return when (hardwareLevel) {
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_LEGACY -> CameraLevel.LEGACY
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_LIMITED -> CameraLevel.LIMITED
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_FULL -> CameraLevel.FULL
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_3 -> CameraLevel.LEVEL_3
            else -> CameraLevel.LEGACY
        }
    }
    
    private fun isManualExposureSupported(characteristics: CameraCharacteristics): Boolean {
        val availableModes = characteristics.get(CameraCharacteristics.CONTROL_AE_AVAILABLE_MODES)
        return availableModes?.contains(CameraCharacteristics.CONTROL_AE_MODE_OFF) == true
    }
    
    private fun isRawSupported(characteristics: CameraCharacteristics): Boolean {
        val capabilities = characteristics.get(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES)
        return capabilities?.contains(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_RAW) == true
    }
    
    private fun areAdvancedFeaturesSupported(characteristics: CameraCharacteristics): Boolean {
        val capabilities = characteristics.get(CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES)
        return capabilities?.any { capability ->
            capability == CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_MANUAL_SENSOR ||
            capability == CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_MANUAL_POST_PROCESSING ||
            capability == CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_RAW
        } == true
    }
    
    private fun getExposureTimeRange(characteristics: CameraCharacteristics): Pair<Long, Long> {
        val range = characteristics.get(CameraCharacteristics.SENSOR_INFO_EXPOSURE_TIME_RANGE)
        return if (range != null) {
            Pair(range.lower, range.upper)
        } else {
            Pair(1000000L, 1000000000L) // Default: 1ms to 1s
        }
    }
    
    private fun getIsoRange(characteristics: CameraCharacteristics): IntRange? {
        val range = characteristics.get(CameraCharacteristics.SENSOR_INFO_SENSITIVITY_RANGE)
        return range?.let { IntRange(it.lower, it.upper) }
    }
    
    private fun determineStrategy(
        level: CameraLevel,
        manualExposureSupported: Boolean,
        advancedFeaturesSupported: Boolean
    ): ShutterSpeedStrategy {
        return when {
            level == CameraLevel.LEVEL_3 && advancedFeaturesSupported -> ShutterSpeedStrategy.DIRECT_CAMERA2
            level == CameraLevel.FULL && manualExposureSupported -> ShutterSpeedStrategy.CAMERAX_INTEROP_ADVANCED
            (level == CameraLevel.LIMITED || level == CameraLevel.FULL) && manualExposureSupported -> ShutterSpeedStrategy.CAMERAX_INTEROP_BASIC
            else -> ShutterSpeedStrategy.EXPOSURE_COMPENSATION
        }
    }
    
    private fun buildDeviceInfo(hardwareLevel: Int, manualExposure: Boolean, rawSupported: Boolean): String {
        val levelName = when (hardwareLevel) {
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_LEGACY -> "LEGACY"
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_LIMITED -> "LIMITED"
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_FULL -> "FULL"
            CameraCharacteristics.INFO_SUPPORTED_HARDWARE_LEVEL_3 -> "LEVEL_3"
            else -> "UNKNOWN"
        }
        
        return "${Build.MANUFACTURER} ${Build.MODEL} (API ${Build.VERSION.SDK_INT}, Camera2 $levelName, Manual: $manualExposure, RAW: $rawSupported)"
    }
}
