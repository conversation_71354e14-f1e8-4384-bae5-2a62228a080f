package com.apparence.camerawesome.cameraX

import android.annotation.SuppressLint
import android.content.Context
import android.hardware.camera2.CaptureRequest
import android.util.Log
import androidx.camera.camera2.interop.Camera2CameraControl
import androidx.camera.camera2.interop.CaptureRequestOptions
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.Camera
import androidx.camera.core.ImageCapture
import com.apparence.camerawesome.CamerawesomePlugin
import java.io.File

/**
 * Comprehensive shutter speed controller that implements tiered strategies
 * based on device capabilities and provides robust fallback mechanisms.
 */
class ShutterSpeedController(private val context: Context) {
    
    companion object {
        private const val TAG = "ShutterSpeedController"
    }
    
    private val capabilityDetector = CameraCapabilityDetector(context)
    private val hybridCaptureManager = HybridCaptureManager(context)
    private var currentCapabilities: CameraCapabilityDetector.CameraCapabilities? = null
    private var currentShutterSpeedSeconds: Double = -1.0
    
    /**
     * Initialize the controller with camera capabilities
     */
    fun initialize(camera: Camera) {
        currentCapabilities = capabilityDetector.detectCapabilitiesFromCamera(camera)
        Log.i(TAG, "Initialized with strategy: ${currentCapabilities?.strategy}")
        Log.i(TAG, "Device info: ${currentCapabilities?.deviceInfo}")
    }
    
    /**
     * Set shutter speed using the optimal strategy for the current device
     */
    @OptIn(ExperimentalCamera2Interop::class)
    fun setShutterSpeed(camera: Camera, shutterSpeedSeconds: Double): Boolean {
        currentShutterSpeedSeconds = shutterSpeedSeconds
        val capabilities = currentCapabilities ?: run {
            Log.w(TAG, "Capabilities not initialized, using fallback")
            initialize(camera)
            currentCapabilities!!
        }
        
        Log.d(TAG, "Setting shutter speed: ${shutterSpeedSeconds}s using strategy: ${capabilities.strategy}")
        
        return when (capabilities.strategy) {
            CameraCapabilityDetector.ShutterSpeedStrategy.DIRECT_CAMERA2 -> {
                setShutterSpeedDirectCamera2(camera, shutterSpeedSeconds, capabilities)
            }
            CameraCapabilityDetector.ShutterSpeedStrategy.CAMERAX_INTEROP_ADVANCED -> {
                setShutterSpeedCameraXAdvanced(camera, shutterSpeedSeconds, capabilities)
            }
            CameraCapabilityDetector.ShutterSpeedStrategy.CAMERAX_INTEROP_BASIC -> {
                setShutterSpeedCameraXBasic(camera, shutterSpeedSeconds, capabilities)
            }
            CameraCapabilityDetector.ShutterSpeedStrategy.EXPOSURE_COMPENSATION -> {
                setShutterSpeedExposureCompensation(camera, shutterSpeedSeconds)
            }
        }
    }
    
    /**
     * Capture photo using the optimal method for manual shutter speed
     */
    suspend fun capturePhoto(
        camera: Camera,
        imageCapture: ImageCapture,
        outputFile: File
    ): Boolean {
        val capabilities = currentCapabilities ?: return false
        
        return if (currentShutterSpeedSeconds > 0 && capabilities.supportsManualExposure) {
            // Use hybrid capture for manual shutter speed
            Log.d(TAG, "Using hybrid capture for manual shutter speed")
            hybridCaptureManager.captureWithManualShutterSpeed(
                camera, currentShutterSpeedSeconds, outputFile, imageCapture
            )
        } else {
            // Use standard CameraX capture for auto mode
            Log.d(TAG, "Using standard CameraX capture")
            false // Indicates to use standard CameraX capture
        }
    }
    
    /**
     * Strategy for Camera3/LEVEL_3 devices - Direct Camera2 API with advanced features
     */
    @OptIn(ExperimentalCamera2Interop::class)
    private fun setShutterSpeedDirectCamera2(
        camera: Camera,
        shutterSpeedSeconds: Double,
        capabilities: CameraCapabilityDetector.CameraCapabilities
    ): Boolean {
        return try {
            val cameraControl = camera.cameraControl
            val camera2Control = Camera2CameraControl.from(cameraControl)
            
            if (shutterSpeedSeconds < 0) {
                // Auto mode with advanced settings
                val captureRequestOptions = CaptureRequestOptions.Builder()
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AWB_MODE, CaptureRequest.CONTROL_AWB_MODE_AUTO)
                    .setCaptureRequestOption(CaptureRequest.NOISE_REDUCTION_MODE, CaptureRequest.NOISE_REDUCTION_MODE_HIGH_QUALITY)
                    .setCaptureRequestOption(CaptureRequest.EDGE_MODE, CaptureRequest.EDGE_MODE_HIGH_QUALITY)
                    .build()
                
                camera2Control.captureRequestOptions = captureRequestOptions
            } else {
                // Manual mode with advanced settings
                val shutterSpeedNanos = (shutterSpeedSeconds * 1_000_000_000).toLong()
                val clampedShutterSpeed = shutterSpeedNanos.coerceIn(
                    capabilities.minExposureTimeNs,
                    capabilities.maxExposureTimeNs
                )
                
                val iso = calculateOptimalIso(shutterSpeedSeconds, capabilities)
                
                val captureRequestOptions = CaptureRequestOptions.Builder()
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_OFF)
                    .setCaptureRequestOption(CaptureRequest.SENSOR_EXPOSURE_TIME, clampedShutterSpeed)
                    .setCaptureRequestOption(CaptureRequest.SENSOR_SENSITIVITY, iso)
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AWB_MODE, CaptureRequest.CONTROL_AWB_MODE_AUTO)
                    .setCaptureRequestOption(CaptureRequest.NOISE_REDUCTION_MODE, CaptureRequest.NOISE_REDUCTION_MODE_HIGH_QUALITY)
                    .setCaptureRequestOption(CaptureRequest.EDGE_MODE, CaptureRequest.EDGE_MODE_HIGH_QUALITY)
                    .setCaptureRequestOption(CaptureRequest.COLOR_CORRECTION_MODE, CaptureRequest.COLOR_CORRECTION_MODE_HIGH_QUALITY)
                    .build()
                
                camera2Control.captureRequestOptions = captureRequestOptions
                camera2Control.addCaptureRequestOptions(captureRequestOptions)
                
                Log.d(TAG, "Applied advanced Camera2 settings: ${clampedShutterSpeed}ns, ISO $iso")
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set shutter speed with direct Camera2", e)
            // Fallback to advanced CameraX interop
            setShutterSpeedCameraXAdvanced(camera, shutterSpeedSeconds, capabilities)
        }
    }
    
    /**
     * Strategy for Camera2 FULL devices - Advanced CameraX interop
     */
    @OptIn(ExperimentalCamera2Interop::class)
    private fun setShutterSpeedCameraXAdvanced(
        camera: Camera,
        shutterSpeedSeconds: Double,
        capabilities: CameraCapabilityDetector.CameraCapabilities
    ): Boolean {
        return try {
            val cameraControl = camera.cameraControl
            val camera2Control = Camera2CameraControl.from(cameraControl)
            
            if (shutterSpeedSeconds < 0) {
                val captureRequestOptions = CaptureRequestOptions.Builder()
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)
                    .build()
                
                camera2Control.captureRequestOptions = captureRequestOptions
            } else {
                val shutterSpeedNanos = (shutterSpeedSeconds * 1_000_000_000).toLong()
                val clampedShutterSpeed = shutterSpeedNanos.coerceIn(
                    capabilities.minExposureTimeNs,
                    capabilities.maxExposureTimeNs
                )
                
                val iso = calculateOptimalIso(shutterSpeedSeconds, capabilities)
                
                val captureRequestOptions = CaptureRequestOptions.Builder()
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_OFF)
                    .setCaptureRequestOption(CaptureRequest.SENSOR_EXPOSURE_TIME, clampedShutterSpeed)
                    .setCaptureRequestOption(CaptureRequest.SENSOR_SENSITIVITY, iso)
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AWB_MODE, CaptureRequest.CONTROL_AWB_MODE_AUTO)
                    .build()
                
                camera2Control.captureRequestOptions = captureRequestOptions
                
                Log.d(TAG, "Applied advanced CameraX interop: ${clampedShutterSpeed}ns, ISO $iso")
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set shutter speed with advanced CameraX interop", e)
            // Fallback to basic CameraX interop
            setShutterSpeedCameraXBasic(camera, shutterSpeedSeconds, capabilities)
        }
    }
    
    /**
     * Strategy for Camera2 LIMITED devices - Basic CameraX interop
     */
    @OptIn(ExperimentalCamera2Interop::class)
    private fun setShutterSpeedCameraXBasic(
        camera: Camera,
        shutterSpeedSeconds: Double,
        capabilities: CameraCapabilityDetector.CameraCapabilities
    ): Boolean {
        return try {
            val cameraControl = camera.cameraControl
            val camera2Control = Camera2CameraControl.from(cameraControl)
            
            if (shutterSpeedSeconds < 0) {
                val captureRequestOptions = CaptureRequestOptions.Builder()
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_ON)
                    .build()
                
                camera2Control.captureRequestOptions = captureRequestOptions
            } else {
                val shutterSpeedNanos = (shutterSpeedSeconds * 1_000_000_000).toLong()
                val clampedShutterSpeed = shutterSpeedNanos.coerceIn(
                    capabilities.minExposureTimeNs,
                    capabilities.maxExposureTimeNs
                )
                
                val captureRequestOptions = CaptureRequestOptions.Builder()
                    .setCaptureRequestOption(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_OFF)
                    .setCaptureRequestOption(CaptureRequest.SENSOR_EXPOSURE_TIME, clampedShutterSpeed)
                    .setCaptureRequestOption(CaptureRequest.SENSOR_SENSITIVITY, 800)
                    .build()
                
                camera2Control.captureRequestOptions = captureRequestOptions
                
                Log.d(TAG, "Applied basic CameraX interop: ${clampedShutterSpeed}ns")
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set shutter speed with basic CameraX interop", e)
            // Fallback to exposure compensation
            setShutterSpeedExposureCompensation(camera, shutterSpeedSeconds)
        }
    }
    
    /**
     * Fallback strategy for LEGACY devices - Exposure compensation
     */
    private fun setShutterSpeedExposureCompensation(
        camera: Camera,
        shutterSpeedSeconds: Double
    ): Boolean {
        return try {
            val cameraControl = camera.cameraControl
            val cameraInfo = camera.cameraInfo
            
            if (shutterSpeedSeconds < 0) {
                cameraControl.setExposureCompensationIndex(0)
            } else {
                val exposureState = cameraInfo.exposureState
                if (exposureState.isExposureCompensationSupported) {
                    val baseShutterSpeed = 1.0 / 60.0
                    val compensationSteps = kotlin.math.log2(shutterSpeedSeconds / baseShutterSpeed)
                    val range = exposureState.exposureCompensationRange
                    val clampedCompensation = compensationSteps.toInt().coerceIn(range.lower, range.upper)
                    
                    cameraControl.setExposureCompensationIndex(clampedCompensation)
                    
                    Log.d(TAG, "Applied exposure compensation: $clampedCompensation")
                } else {
                    Log.w(TAG, "Exposure compensation not supported")
                    return false
                }
            }
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to set exposure compensation", e)
            false
        }
    }
    
    private fun calculateOptimalIso(
        shutterSpeedSeconds: Double,
        capabilities: CameraCapabilityDetector.CameraCapabilities
    ): Int {
        val isoRange = capabilities.supportedIsoRange ?: return 800
        
        return when {
            shutterSpeedSeconds >= 2.0 -> isoRange.first.coerceAtLeast(1600).coerceAtMost(isoRange.last)
            shutterSpeedSeconds >= 1.0 -> isoRange.first.coerceAtLeast(800).coerceAtMost(isoRange.last)
            shutterSpeedSeconds >= 0.5 -> isoRange.first.coerceAtLeast(400).coerceAtMost(isoRange.last)
            shutterSpeedSeconds >= 0.25 -> isoRange.first.coerceAtLeast(200).coerceAtMost(isoRange.last)
            else -> isoRange.first.coerceAtLeast(100).coerceAtMost(isoRange.last)
        }
    }
    
    fun getCurrentCapabilities(): CameraCapabilityDetector.CameraCapabilities? = currentCapabilities
}
