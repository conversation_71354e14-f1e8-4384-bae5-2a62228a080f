package com.apparence.camerawesome.cameraX

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.ImageFormat
import android.hardware.camera2.*
import android.media.Image
import android.media.ImageReader
import android.os.Handler
import android.os.HandlerThread
import android.util.Log
import android.util.Size
import androidx.camera.camera2.interop.Camera2CameraInfo
import androidx.camera.camera2.interop.ExperimentalCamera2Interop
import androidx.camera.core.Camera
import androidx.camera.core.ImageCapture
import com.apparence.camerawesome.CamerawesomePlugin
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import java.io.FileOutputStream
import kotlin.coroutines.resume

/**
 * Hybrid capture manager that uses Camera2 API directly for manual shutter speed captures
 * when CameraX Camera2 interop is insufficient.
 */
class HybridCaptureManager(private val context: Context) {
    
    companion object {
        private const val TAG = "HybridCaptureManager"
    }
    
    private var cameraDevice: CameraDevice? = null
    private var captureSession: CameraCaptureSession? = null
    private var backgroundThread: HandlerThread? = null
    private var backgroundHandler: Handler? = null
    private var imageReader: ImageReader? = null
    
    private val capabilities by lazy { 
        CameraCapabilityDetector(context).detectCapabilities() 
    }
    
    /**
     * Captures a photo using Camera2 API directly with manual shutter speed control
     */
    @OptIn(ExperimentalCamera2Interop::class)
    suspend fun captureWithManualShutterSpeed(
        camera: Camera,
        shutterSpeedSeconds: Double,
        outputFile: File,
        imageCapture: ImageCapture
    ): Boolean = suspendCancellableCoroutine { continuation ->
        
        try {
            Log.d(TAG, "Starting hybrid capture with shutter speed: ${shutterSpeedSeconds}s")
            
            // Get camera ID from CameraX camera
            val camera2Info = Camera2CameraInfo.from(camera.cameraInfo)
            val cameraId = camera2Info.cameraId
            
            // Start background thread for camera operations
            startBackgroundThread()
            
            // Open camera device
            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            
            cameraManager.openCamera(cameraId, object : CameraDevice.StateCallback() {
                override fun onOpened(camera: CameraDevice) {
                    cameraDevice = camera
                    createCaptureSession(camera, shutterSpeedSeconds, outputFile) { success ->
                        cleanup()
                        continuation.resume(success)
                    }
                }
                
                override fun onDisconnected(camera: CameraDevice) {
                    Log.w(TAG, "Camera disconnected during hybrid capture")
                    cleanup()
                    continuation.resume(false)
                }
                
                override fun onError(camera: CameraDevice, error: Int) {
                    Log.e(TAG, "Camera error during hybrid capture: $error")
                    cleanup()
                    continuation.resume(false)
                }
            }, backgroundHandler)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start hybrid capture", e)
            cleanup()
            continuation.resume(false)
        }
    }
    
    @SuppressLint("MissingPermission")
    private fun createCaptureSession(
        camera: CameraDevice,
        shutterSpeedSeconds: Double,
        outputFile: File,
        callback: (Boolean) -> Unit
    ) {
        try {
            // Get optimal capture size
            val cameraManager = context.getSystemService(Context.CAMERA_SERVICE) as CameraManager
            val characteristics = cameraManager.getCameraCharacteristics(camera.id)
            val map = characteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP)
            val captureSize = map?.getOutputSizes(ImageFormat.JPEG)?.maxByOrNull { it.width * it.height }
                ?: Size(1920, 1080)
            
            Log.d(TAG, "Using capture size: ${captureSize.width}x${captureSize.height}")
            
            // Create ImageReader for capture
            imageReader = ImageReader.newInstance(
                captureSize.width,
                captureSize.height,
                ImageFormat.JPEG,
                1
            )
            
            imageReader?.setOnImageAvailableListener({ reader ->
                val image = reader.acquireLatestImage()
                saveImageToFile(image, outputFile) { success ->
                    image.close()
                    callback(success)
                }
            }, backgroundHandler)
            
            // Create capture session
            camera.createCaptureSession(
                listOf(imageReader?.surface),
                object : CameraCaptureSession.StateCallback() {
                    override fun onConfigured(session: CameraCaptureSession) {
                        captureSession = session
                        captureStillPicture(session, shutterSpeedSeconds, callback)
                    }
                    
                    override fun onConfigureFailed(session: CameraCaptureSession) {
                        Log.e(TAG, "Failed to configure capture session")
                        callback(false)
                    }
                },
                backgroundHandler
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to create capture session", e)
            callback(false)
        }
    }
    
    private fun captureStillPicture(
        session: CameraCaptureSession,
        shutterSpeedSeconds: Double,
        callback: (Boolean) -> Unit
    ) {
        try {
            val reader = imageReader ?: run {
                callback(false)
                return
            }
            
            // Create capture request with manual exposure settings
            val captureBuilder = cameraDevice?.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE)
            captureBuilder?.addTarget(reader.surface)
            
            // Apply manual shutter speed settings
            val shutterSpeedNanos = (shutterSpeedSeconds * 1_000_000_000).toLong()
            
            // Clamp to device limits
            val clampedShutterSpeed = shutterSpeedNanos.coerceIn(
                capabilities.minExposureTimeNs,
                capabilities.maxExposureTimeNs
            )
            
            captureBuilder?.apply {
                set(CaptureRequest.CONTROL_AE_MODE, CaptureRequest.CONTROL_AE_MODE_OFF)
                set(CaptureRequest.SENSOR_EXPOSURE_TIME, clampedShutterSpeed)
                
                // Set ISO based on device capabilities
                val iso = capabilities.supportedIsoRange?.let { range ->
                    // Use higher ISO for longer exposures to maintain image brightness
                    when {
                        shutterSpeedSeconds >= 1.0 -> range.first.coerceAtLeast(800)
                        shutterSpeedSeconds >= 0.5 -> range.first.coerceAtLeast(400)
                        else -> range.first.coerceAtLeast(200)
                    }.coerceAtMost(range.last)
                } ?: 800
                
                set(CaptureRequest.SENSOR_SENSITIVITY, iso)
                set(CaptureRequest.CONTROL_AWB_MODE, CaptureRequest.CONTROL_AWB_MODE_AUTO)
                
                // Enhanced settings for better image quality
                if (capabilities.supportsAdvancedFeatures) {
                    set(CaptureRequest.NOISE_REDUCTION_MODE, CaptureRequest.NOISE_REDUCTION_MODE_HIGH_QUALITY)
                    set(CaptureRequest.EDGE_MODE, CaptureRequest.EDGE_MODE_HIGH_QUALITY)
                    set(CaptureRequest.COLOR_CORRECTION_MODE, CaptureRequest.COLOR_CORRECTION_MODE_HIGH_QUALITY)
                }
                
                Log.d(TAG, "Capturing with: ${clampedShutterSpeed}ns exposure, ISO $iso")
            }
            
            val captureRequest = captureBuilder?.build()
            if (captureRequest != null) {
                session.capture(captureRequest, object : CameraCaptureSession.CaptureCallback() {
                    override fun onCaptureCompleted(
                        session: CameraCaptureSession,
                        request: CaptureRequest,
                        result: TotalCaptureResult
                    ) {
                        Log.d(TAG, "Capture completed successfully")
                        // Image will be saved by ImageReader callback
                    }
                    
                    override fun onCaptureFailed(
                        session: CameraCaptureSession,
                        request: CaptureRequest,
                        failure: CaptureFailure
                    ) {
                        Log.e(TAG, "Capture failed: ${failure.reason}")
                        callback(false)
                    }
                }, backgroundHandler)
            } else {
                callback(false)
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to capture still picture", e)
            callback(false)
        }
    }
    
    private fun saveImageToFile(image: Image, outputFile: File, callback: (Boolean) -> Unit) {
        try {
            val buffer = image.planes[0].buffer
            val bytes = ByteArray(buffer.remaining())
            buffer.get(bytes)
            
            outputFile.parentFile?.mkdirs()
            FileOutputStream(outputFile).use { output ->
                output.write(bytes)
            }
            
            Log.d(TAG, "Image saved successfully to: ${outputFile.absolutePath}")
            callback(true)
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save image", e)
            callback(false)
        }
    }
    
    private fun startBackgroundThread() {
        backgroundThread = HandlerThread("CameraBackground").also { it.start() }
        backgroundHandler = Handler(backgroundThread?.looper!!)
    }
    
    private fun cleanup() {
        try {
            captureSession?.close()
            captureSession = null
            
            cameraDevice?.close()
            cameraDevice = null
            
            imageReader?.close()
            imageReader = null
            
            backgroundThread?.quitSafely()
            backgroundThread?.join()
            backgroundThread = null
            backgroundHandler = null
            
        } catch (e: Exception) {
            Log.e(TAG, "Error during cleanup", e)
        }
    }
}
